{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "socket": "node socketServer.js", "dev:all": "concurrently \"npm run dev\" \"npm run socket\"", "start:all": "concurrently \"npm run start\" \"npm run socket\"", "build:css": "sass styles/splashScreen.module.scss styles/splashScreen.module.css", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.3", "@socket.io/redis-adapter": "^8.3.0", "@tanstack/react-query": "^5.67.3", "@types/date-fns": "^2.5.3", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.7.9", "bcryptjs": "^3.0.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cookies-next": "^5.1.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase": "^11.10.0", "firebase-admin": "^13.4.0", "framer-motion": "^12.5.0", "fuse.js": "^7.1.0", "heic2any": "^0.0.4", "input-otp": "^1.4.2", "ioredis": "^5.6.0", "jose": "^6.0.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "moment": "^2.30.1", "mongoose": "^8.10.1", "motion": "^12.4.3", "next": "^15.2.0", "next-themes": "^0.4.4", "nodemailer": "^6.10.0", "pg-sdk-node": "https://phonepe.mycloudrepo.io/public/repositories/phonepe-pg-sdk-node/releases/v2/phonepe-pg-sdk-node.tgz", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-image-crop": "^11.0.7", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.2.0", "recharts": "^2.15.1", "sass": "^1.85.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "web-push": "^3.6.7", "ws": "^8.18.1", "zod": "^3.24.2", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/web-push": "^3.6.4", "@types/ws": "^8.5.14", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwind-scrollbar": "^3.0.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}